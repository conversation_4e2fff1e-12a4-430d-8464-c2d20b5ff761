import { fabric } from "fabric";
import { useCallback, useRef, useState } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseHistoryProps {
  canvas: fabric.Canvas | null;
  saveCallback?: (values: {
    json: string;
    height: number;
    width: number;
  }) => void;
};

export const useHistory = ({ canvas, saveCallback }: UseHistoryProps) => {
  const [historyIndex, setHistoryIndex] = useState(0);
  const canvasHistory = useRef<string[]>([]);
  const skipSave = useRef(false);

  const canUndo = useCallback(() => {
    return historyIndex > 0;
  }, [historyIndex]);

  const canRedo = useCallback(() => {
    return historyIndex < canvasHistory.current.length - 1;
  }, [historyIndex]);

  const lastSaveTime = useRef(0);
  const lastSaveJson = useRef<string>('');

  const save = useCallback((skip = false) => {
    if (!canvas) return;

    const currentState = canvas.toJSON(JSON_KEYS);
    const json = JSON.stringify(currentState);

    // Prevent excessive saves - only save if content actually changed and enough time has passed
    const now = Date.now();
    if (!skip && !skipSave.current && (json !== lastSaveJson.current || now - lastSaveTime.current > 1000)) {
      canvasHistory.current.push(json);
      setHistoryIndex(canvasHistory.current.length - 1);
      lastSaveJson.current = json;
      lastSaveTime.current = now;

      const workspace = canvas
        .getObjects()
        .find((object) => object.name === "clip");
      const height = workspace?.height || 0;
      const width = workspace?.width || 0;

      saveCallback?.({ json, height, width });
    }
  },
  [
    canvas,
    saveCallback,
  ]);

  const undo = useCallback(() => {
    if (canUndo()) {
      skipSave.current = true;
      canvas?.clear().renderAll();

      const previousIndex = historyIndex - 1;
      const previousState = JSON.parse(
        canvasHistory.current[previousIndex]
      );

      canvas?.loadFromJSON(previousState, () => {
        // CRITICAL FIX: Restore interaction properties after undo
        canvas.getObjects().forEach(obj => {
          const objAny = obj as any;

          // Set default interaction properties if they're undefined or false
          if (objAny.selectable === undefined || objAny.selectable === null) {
            objAny.selectable = true;
          }
          if (objAny.evented === undefined || objAny.evented === null) {
            objAny.evented = true;
          }
          if (objAny.hasControls === undefined || objAny.hasControls === null) {
            objAny.hasControls = true;
          }
          if (objAny.hasBorders === undefined || objAny.hasBorders === null) {
            objAny.hasBorders = true;
          }

          // Special handling for workspace objects
          if (objAny.name === 'clip') {
            objAny.selectable = false;
            objAny.evented = false;
            objAny.hasControls = false;
            objAny.hasBorders = false;
          }
        });

        canvas.renderAll();
        setHistoryIndex(previousIndex);
        skipSave.current = false;
      });
    }
  }, [canUndo, canvas, historyIndex]);

  const redo = useCallback(() => {
    if (canRedo()) {
      skipSave.current = true;
      canvas?.clear().renderAll();

      const nextIndex = historyIndex + 1;
      const nextState = JSON.parse(
        canvasHistory.current[nextIndex]
      );

      canvas?.loadFromJSON(nextState, () => {
        // CRITICAL FIX: Restore interaction properties after redo
        canvas.getObjects().forEach(obj => {
          const objAny = obj as any;

          // Set default interaction properties if they're undefined or false
          if (objAny.selectable === undefined || objAny.selectable === null) {
            objAny.selectable = true;
          }
          if (objAny.evented === undefined || objAny.evented === null) {
            objAny.evented = true;
          }
          if (objAny.hasControls === undefined || objAny.hasControls === null) {
            objAny.hasControls = true;
          }
          if (objAny.hasBorders === undefined || objAny.hasBorders === null) {
            objAny.hasBorders = true;
          }

          // Special handling for workspace objects
          if (objAny.name === 'clip') {
            objAny.selectable = false;
            objAny.evented = false;
            objAny.hasControls = false;
            objAny.hasBorders = false;
          }
        });

        canvas.renderAll();
        setHistoryIndex(nextIndex);
        skipSave.current = false;
      });
    }
  }, [canvas, historyIndex, canRedo]);

  return { 
    save,
    canUndo,
    canRedo,
    undo,
    redo,
    setHistoryIndex,
    canvasHistory,
  };
};
