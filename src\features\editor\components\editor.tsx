"use client";

import { fabric } from "fabric";
import debounce from "lodash.debounce";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { ResponseType } from "@/features/projects/api/use-get-project";
import { useUpdateProject } from "@/features/projects/api/use-update-project";
import { useThumbnailGenerator } from "@/features/editor/hooks/use-thumbnail-generator";

import { 
  ActiveTool, 
  selectionDependentTools
} from "@/features/editor/types";
import { Navbar } from "@/features/editor/components/navbar";
import { Footer } from "@/features/editor/components/footer";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Sidebar } from "@/features/editor/components/sidebar";
import { Toolbar } from "@/features/editor/components/toolbar";
import { ShapeSidebar } from "@/features/editor/components/shape-sidebar";
import { FillColorSidebar } from "@/features/editor/components/fill-color-sidebar";
import { StrokeColorSidebar } from "@/features/editor/components/stroke-color-sidebar";
import { StrokeWidthSidebar } from "@/features/editor/components/stroke-width-sidebar";
import { OpacitySidebar } from "@/features/editor/components/opacity-sidebar";
import { TextSidebar } from "@/features/editor/components/text-sidebar";
import { FontSidebar } from "@/features/editor/components/font-sidebar";
import { ImageSidebar } from "@/features/editor/components/image-sidebar";
import { FilterSidebar } from "@/features/editor/components/filter-sidebar";
import { DrawSidebar } from "@/features/editor/components/draw-sidebar";
import { AiSidebar } from "@/features/editor/components/ai-sidebar";
import { AiToolsSidebar } from "@/features/editor/components/ai-tools-sidebar";
import { TemplateSidebar } from "@/features/editor/components/template-sidebar";
import { RemoveBgSidebar } from "@/features/editor/components/remove-bg-sidebar";
import { SettingsSidebar } from "@/features/editor/components/settings-sidebar";
import { TemplateConfigSidebar } from "@/features/editor/components/template-config-sidebar";

interface EditorProps {
  initialData: ResponseType["data"];
  initialActiveTool?: ActiveTool;
};

export const Editor = ({ initialData, initialActiveTool = "select" }: EditorProps) => {
  const { mutate } = useUpdateProject(initialData.id);

  // Template Editor - Component Mount
  useEffect(() => {
    // Component mounted - logging removed

    // Try to parse JSON structure
    if (initialData.json) {
      try {
        const parsedJson = JSON.parse(initialData.json);
        // JSON structure parsed - logging removed
      } catch (error) {
        // Error parsing initial JSON - logging removed
      }
    }

    return () => {
      // Component unmounting - logging removed
    };
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSave = useCallback(
    debounce(
      (values: {
        json: string,
        height: number,
        width: number,
      }) => {
        // Debounced save triggered - logging removed
        mutate(values);
    },
    5000 // Increased to 5 seconds for better performance
  ), [mutate]);

  const [activeTool, setActiveTool] = useState<ActiveTool>(initialActiveTool);

  const onClearSelection = useCallback(() => {
    setActiveTool(currentTool => {
      if (selectionDependentTools.includes(currentTool)) {
        // Clearing selection, changing tool - logging removed
        return "select";
      }
      return currentTool;
    });
  }, []); // Remove activeTool dependency to prevent re-renders

  // Memoize editor configuration to prevent unnecessary re-initializations
  const editorConfig = useMemo(() => ({
    defaultState: initialData.json,
    defaultWidth: initialData.width,
    defaultHeight: initialData.height,
    clearSelectionCallback: onClearSelection,
    saveCallback: debouncedSave,
  }), [initialData.json, initialData.width, initialData.height, onClearSelection, debouncedSave]);

  // Initializing editor hook - logging removed
  const { init, editor } = useEditor(editorConfig);

  const handleManualSave = () => {
    // Manual save triggered - logging removed
    if (editor?.canvas) {
      const workspace = editor.canvas
        .getObjects()
        .find((object) => object.name === "clip");
      const height = workspace?.height || initialData.height;
      const width = workspace?.width || initialData.width;
      const json = JSON.stringify(editor.canvas.toJSON());

      // Manual save data - logging removed

      mutate({ json, height, width });
    } else {
      // Manual save failed - no editor or canvas available - logging removed
    }
  };

  // Generate thumbnails automatically when the canvas changes
  const { debouncedGenerateThumbnail, forceRegenerateThumbnail } = useThumbnailGenerator({
    editor,
    projectId: initialData.id,
  });

  // Template Editor - Editor State Changes
  useEffect(() => {
    if (editor) {
      // Editor initialized - logging removed

      // Canvas dimensions - logging removed
      if (editor.canvas) {
        // Canvas dimensions logged - logging removed
      }
    }
  }, [editor]);

  // Trigger thumbnail generation when canvas changes
  useEffect(() => {
    if (editor?.canvas) {
      const handleCanvasChange = () => {
        // Canvas change detected - logging removed
        debouncedGenerateThumbnail();
      };

      const canvas = editor.canvas;
      canvas.on('object:added', handleCanvasChange);
      canvas.on('object:removed', handleCanvasChange);
      canvas.on('object:modified', handleCanvasChange);
      canvas.on('path:created', handleCanvasChange);

      // Canvas event listeners attached - logging removed

      // Generate initial thumbnail when editor is ready (only once)
      setTimeout(() => {
        // Generating initial thumbnail - logging removed
        debouncedGenerateThumbnail();
      }, 2000); // Wait for canvas to be fully ready

      return () => {
        // Removing canvas event listeners - logging removed
        canvas.off('object:added', handleCanvasChange);
        canvas.off('object:removed', handleCanvasChange);
        canvas.off('object:modified', handleCanvasChange);
        canvas.off('path:created', handleCanvasChange);
      };
    }
  }, [editor, debouncedGenerateThumbnail]);

  // Trigger thumbnail generation when editor changes
  useEffect(() => {
    if (editor) {
      // Editor changed - logging removed
      debouncedGenerateThumbnail();
    }
  }, [editor, debouncedGenerateThumbnail]);

  // Track when component is fully mounted and ready
  useEffect(() => {
    if (editor?.canvas && editor.canvas.getObjects().length > 1) {
      // Wait a bit to ensure everything is settled
      const timeout = setTimeout(() => {
        // Component fully mounted and interactive - logging removed
      }, 1000);

      return () => clearTimeout(timeout);
    }
  }, [editor, activeTool, initialData.id, initialData.name]);

  const onChangeActiveTool = useCallback((tool: ActiveTool) => {
    // Active tool changing - logging removed

    if (tool === "draw") {
      // Enabling draw mode - logging removed
      editor?.enableDrawingMode();
    }

    if (activeTool === "draw") {
      // Disabling draw mode - logging removed
      editor?.disableDrawingMode();
    }

    if (tool === "pan") {
      // Enabling pan mode - logging removed
      editor?.enablePanMode();
    }

    if (activeTool === "pan") {
      // Disabling pan mode - logging removed
      editor?.disablePanMode();
    }

    if (tool === activeTool) {
      // Same tool selected, switching to select - logging removed
      return setActiveTool("select");
    }

    setActiveTool(tool);
  }, [activeTool, editor]);

  const canvasRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Canvas initialization effect triggered - logging removed

    const initializeCanvas = () => {
      // Attempting canvas initialization - logging removed

      // Ensure container is available and has dimensions
      if (!containerRef.current) {
        // Container ref not available - logging removed
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      // Container dimensions - logging removed

      // Don't initialize if container has no dimensions
      if (containerWidth === 0 || containerHeight === 0) {
        // Container has zero dimensions, delaying canvas initialization - logging removed
        return false;
      }

      // Creating fabric canvas - logging removed
      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      // Fabric canvas created, initializing editor - logging removed
      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      // Canvas initialization completed successfully - logging removed
      return canvas;
    };

    // Try to initialize immediately
    // Starting immediate canvas initialization - logging removed
    const canvas = initializeCanvas();

    if (!canvas) {
      // Initial canvas initialization failed, scheduling retry - logging removed
      // If initialization failed, retry after a short delay
      const timeoutId = setTimeout(() => {
        // Retrying canvas initialization - logging removed
        const retryCanvas = initializeCanvas();
        if (!retryCanvas) {
          // Failed to initialize canvas after retry - logging removed
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }

    return () => {
      canvas.dispose();
    };
  }, [init]);

  // Log when component is about to render
  // Component rendering - logging removed

  return (
    <div className="h-full flex flex-col">
      <Navbar
        id={initialData.id}
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={onChangeActiveTool}
        onSave={handleManualSave}
        initialData={{
          name: initialData.name,
          isCustomizable: initialData.isCustomizable || false,
        }}
      />
      <div className="absolute h-[calc(100%-68px)] w-full top-[68px] flex">
        <Sidebar
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ShapeSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FillColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeWidthSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <OpacitySidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TextSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FontSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ImageSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FilterSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiToolsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <RemoveBgSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <DrawSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <SettingsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateConfigSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
          projectId={initialData.id}
          initialData={{
            isCustomizable: initialData.isCustomizable || false,
            editableLayers: initialData.editableLayers,
          }}
        />
        <main className="bg-muted flex-1 overflow-auto relative flex flex-col">
          <Toolbar
            editor={editor}
            activeTool={activeTool}
            onChangeActiveTool={onChangeActiveTool}
            key={JSON.stringify(editor?.canvas.getActiveObject())}
          />
          <div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
            <canvas ref={canvasRef} />
          </div>
          <Footer editor={editor} />
        </main>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(Editor);
