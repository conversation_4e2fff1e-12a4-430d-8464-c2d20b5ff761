import { useEffect, useRef } from "react";
import { fabric } from "fabric";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
  initialWidth: React.MutableRefObject<number>;
  initialHeight: React.MutableRefObject<number>;
};

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
  initialWidth,
  initialHeight,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current && initialState?.current && canvas) {
      try {
        const data = JSON.parse(initialState.current);
        console.log('Loading template data:', {
          hasData: !!data,
          objectCount: data?.objects?.length || 0,
          canvasWidth: canvas.getWidth(),
          canvasHeight: canvas.getHeight()
        });

        // Validate canvas dimensions - but don't skip if they're zero, just warn
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();

        if (canvasWidth <= 0 || canvasHeight <= 0) {
          console.warn("Canvas has invalid dimensions, but proceeding with loadFromJSON");
        }

        canvas.loadFromJSON(data, () => {
          console.log('Canvas loaded, objects count:', canvas.getObjects().length);

          // Find the original workspace/clip object from the template
          const originalWorkspace = canvas.getObjects().find(obj => obj.name === 'clip');
          let offsetX = 0;
          let offsetY = 0;

          if (originalWorkspace) {
            // Calculate offset from original workspace position to center
            const canvasCenter = canvas.getCenter();
            offsetX = canvasCenter.left - (originalWorkspace.left || 0);
            offsetY = canvasCenter.top - (originalWorkspace.top || 0);

            console.log('Found original workspace at:', {
              left: originalWorkspace.left,
              top: originalWorkspace.top,
              canvasCenter,
              offset: { offsetX, offsetY }
            });

            // Remove the original workspace since we create our own
            canvas.remove(originalWorkspace);
          }

          // Post-load validation and fixes
          canvas.getObjects().forEach(obj => {
            // Adjust object positions if we found an offset
            if (offsetX !== 0 || offsetY !== 0) {
              obj.set({
                left: (obj.left || 0) + offsetX,
                top: (obj.top || 0) + offsetY
              });
            }

            // Fix zero dimensions
            if (obj.width === 0 || obj.height === 0) {
              if (obj.width === 0) obj.set('width', 1);
              if (obj.height === 0) obj.set('height', 1);
            }

            // Fix invalid textBaseline values
            if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
              const textObj = obj as fabric.Text;
              const currentBaseline = (textObj as any).textBaseline;
              // Fix 'alphabetical' -> 'alphabetic' and other invalid values
              if (currentBaseline === 'alphabetical') {
                (textObj as any).textBaseline = 'alphabetic';
              } else if (currentBaseline && !['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'].includes(currentBaseline)) {
                (textObj as any).textBaseline = 'middle';
              }
            }
          });

          // Create our own workspace/clip object
          const workspaceWidth = initialWidth?.current || 900;
          const workspaceHeight = initialHeight?.current || 1200;

          const workspace = new fabric.Rect({
            width: workspaceWidth,
            height: workspaceHeight,
            name: "clip",
            fill: "white",
            selectable: false,
            hasControls: false,
            shadow: new fabric.Shadow({
              color: "rgba(0,0,0,0.8)",
              blur: 5,
            }),
          });

          // Add workspace and center it
          canvas.add(workspace);
          canvas.centerObject(workspace);
          canvas.clipPath = workspace;
          canvas.sendToBack(workspace);

          console.log('Created new workspace at center:', {
            left: workspace.left,
            top: workspace.top,
            width: workspaceWidth,
            height: workspaceHeight
          });

          // Initialize history - no auto-zoom for template editor
          const currentState = JSON.stringify(canvas.toJSON(JSON_KEYS));
          canvasHistory.current = [currentState];
          setHistoryIndex(0);

          // Force render after loading
          canvas.requestRenderAll();
        });

        initialized.current = true;
      } catch (error) {
        console.error("Error loading canvas state:", error);
        initialized.current = true; // Prevent infinite retries
      }
    }
  }, [canvas, autoZoom, initialState, canvasHistory, setHistoryIndex]);
};
