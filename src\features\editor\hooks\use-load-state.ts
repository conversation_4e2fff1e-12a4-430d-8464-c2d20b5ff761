import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current && initialState?.current && canvas) {
      // useLoadState: Attempting to load initial state - logging removed
      const loadStartTime = performance.now();

      try {
        const data = JSON.parse(initialState.current);
        // useLoadState: Parsed JSON data - logging removed

        // Log detailed object information
        if (data.objects && data.objects.length > 0) {
          // useLoadState: Object details - logging removed
        }

        // Validate canvas dimensions before loading
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();

        // useLoadState: Canvas dimensions - logging removed

        if (canvasWidth <= 0 || canvasHeight <= 0) {
          // Canvas has invalid dimensions, skipping loadFromJSON - logging removed
          return;
        }

        // useLoadState: Loading JSON into canvas - logging removed
        canvas.loadFromJSON(data, () => {
          const loadEndTime = performance.now();
          const loadDuration = loadEndTime - loadStartTime;

          // useLoadState: JSON loaded successfully - logging removed

          // Ensure all objects have valid dimensions after loading
          let fixedObjectsCount = 0;
          let textBaselineFixesCount = 0;

          canvas.getObjects().forEach(obj => {
            if (obj.width === 0 || obj.height === 0) {
              // Object has zero dimensions after loading - logging removed
              // Set minimum dimensions to prevent rendering errors
              if (obj.width === 0) obj.set('width', 1);
              if (obj.height === 0) obj.set('height', 1);
              fixedObjectsCount++;
            }

            // Fix invalid textBaseline values
            if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
              const textObj = obj as fabric.Text;
              const currentBaseline = (textObj as any).textBaseline;
              // Fix 'alphabetical' -> 'alphabetic' and other invalid values
              if (currentBaseline === 'alphabetical' ||
                  (currentBaseline && currentBaseline !== 'top' && currentBaseline !== 'hanging' &&
                  currentBaseline !== 'middle' && currentBaseline !== 'alphabetic' &&
                  currentBaseline !== 'ideographic' && currentBaseline !== 'bottom')) {
                // Invalid textBaseline value found, setting to "alphabetic" - logging removed
                (textObj as any).textBaseline = 'alphabetic';
                textBaselineFixesCount++;
              }
            }

            // CRITICAL FIX: Ensure all objects have proper interaction properties after JSON load
            // This is essential for objects to be selectable and editable after page reload
            const objAny = obj as any;

            // Set default interaction properties if they're undefined or false
            if (objAny.selectable === undefined || objAny.selectable === null) {
              objAny.selectable = true;
            }
            if (objAny.evented === undefined || objAny.evented === null) {
              objAny.evented = true;
            }
            if (objAny.hasControls === undefined || objAny.hasControls === null) {
              objAny.hasControls = true;
            }
            if (objAny.hasBorders === undefined || objAny.hasBorders === null) {
              objAny.hasBorders = true;
            }

            // Special handling for workspace objects
            if (objAny.name === 'clip') {
              objAny.selectable = false;
              objAny.evented = false;
              objAny.hasControls = false;
              objAny.hasBorders = false;
            }
          });

          if (fixedObjectsCount > 0 || textBaselineFixesCount > 0) {
            // Object fixes applied - logging removed
          }

          const currentState = JSON.stringify(
            canvas.toJSON(JSON_KEYS),
          );

          canvasHistory.current = [currentState];
          setHistoryIndex(0);

          // useLoadState: Calling autoZoom - logging removed
          const autoZoomStartTime = performance.now();

          autoZoom();

          const autoZoomEndTime = performance.now();
          const autoZoomDuration = autoZoomEndTime - autoZoomStartTime;

          // useLoadState: Template loading complete - logging removed
        });
        initialized.current = true;
      } catch (error) {
        // Error loading canvas state - logging removed
        initialized.current = true; // Prevent infinite retries
      }
    }
  },
  [
    canvas,
    autoZoom,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
