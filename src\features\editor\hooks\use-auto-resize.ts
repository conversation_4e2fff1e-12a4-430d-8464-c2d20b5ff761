import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    // autoZoom called - logging removed
    const autoZoomStartTime = performance.now();

    if (!canvas || !container) {
      // autoZoom: Missing canvas or container - logging removed
      return;
    }

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    // autoZoom: Container dimensions - logging removed

    // Ensure we have valid dimensions
    if (width <= 0 || height <= 0) {
      // Container has invalid dimensions for autoZoom - logging removed
      return;
    }

    // Don't set canvas dimensions here - they should be set by ResizeObserver
    // canvas.setWidth(width);
    // canvas.setHeight(height);

    const center = canvas.getCenter();
    // autoZoom: Canvas center - logging removed

    const zoomRatio = 0.85;
    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) {
      // autoZoom: No workspace found - logging removed
      return;
    }

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    // autoZoom: Workspace dimensions - logging removed

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      // Workspace has invalid dimensions for autoZoom - logging removed
      return;
    }

    // @ts-ignore
    const scale = fabric.util.findScaleToFit(localWorkspace, {
      width: width,
      height: height,
    });

    const zoom = zoomRatio * scale;

    // autoZoom: Calculated zoom values - logging removed

    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    // autoZoom: Workspace center - logging removed

    if (
      canvas.width === undefined ||
      canvas.height === undefined ||
      !viewportTransform
    ) {
      // autoZoom: Invalid canvas dimensions or viewport transform - logging removed
      return;
    }

    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];

    canvas.setViewportTransform(viewportTransform);

    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();

      const autoZoomEndTime = performance.now();
      const autoZoomDuration = autoZoomEndTime - autoZoomStartTime;

      // autoZoom completed successfully - logging removed

      // Mark the template editor as fully loaded and ready
      // Page fully loaded and ready for interaction - logging removed
    });
  }, [canvas, container]);

  // Disabled automatic resize observer to prevent canvas interaction issues
  // The ResizeObserver was causing continuous auto-zoom operations that interfered
  // with canvas interactivity. Manual zoom controls are still available.
  useEffect(() => {
    // ResizeObserver disabled to maintain canvas interactivity
    // Users can manually adjust zoom using the zoom controls
    return () => {
      // Cleanup function (no-op since ResizeObserver is disabled)
    };
  }, [canvas, container, autoZoom]);

  return { autoZoom };
};
