import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    if (!canvas || !container) return;

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    // Ensure we have valid dimensions
    if (width <= 0 || height <= 0) {
      console.warn("Container has invalid dimensions for resize", { width, height });
      return;
    }

    // Update canvas dimensions to match container
    canvas.setWidth(width);
    canvas.setHeight(height);

    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) return;

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("Workspace has invalid dimensions", { workspaceWidth, workspaceHeight });
      return;
    }

    // NO ZOOMING - Keep zoom at 1:1 scale as documented
    canvas.setZoom(1);

    // Reset viewport transform to identity matrix (no scaling, no translation initially)
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

    // Center the workspace in the canvas at 1:1 scale
    const canvasCenter = canvas.getCenter();
    localWorkspace.set({
      left: canvasCenter.left,
      top: canvasCenter.top,
      originX: 'center',
      originY: 'center'
    });

    // Update clipPath
    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();
    });

    console.log('Canvas resized - no zoom applied, workspace centered at 1:1 scale');
  }, [canvas, container]);

  useEffect(() => {
    let resizeObserver: ResizeObserver | null = null;

    if (canvas && container) {
      resizeObserver = new ResizeObserver(() => {
        autoZoom();
      });

      resizeObserver.observe(container);
    }

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [canvas, container, autoZoom]);

  // Note: autoZoom is now just canvas resize + workspace centering at 1:1 scale (no actual zooming)
  return { autoZoom };
};
