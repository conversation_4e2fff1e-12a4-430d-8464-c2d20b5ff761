import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
  enableAutoResize?: boolean; // Optional flag to disable automatic resizing
}

export const useAutoResize = ({ canvas, container, enableAutoResize = true }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    if (!canvas || !container) return;

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    // Validate dimensions
    if (width <= 0 || height <= 0) {
      console.warn("Container has invalid dimensions for autoZoom");
      return;
    }

    const center = canvas.getCenter();
    const zoomRatio = 0.85;
    const localWorkspace = canvas.getObjects().find((object) => object.name === "clip");

    if (!localWorkspace) return;

    // Calculate optimal zoom scale
    // @ts-ignore
    const scale = fabric.util.findScaleToFit(localWorkspace, { width, height });
    const zoom = zoomRatio * scale;

    // Apply zoom and center workspace
    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    if (canvas.width && canvas.height && viewportTransform) {
      viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];
      viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];
      canvas.setViewportTransform(viewportTransform);
    }

    // Update clip path
    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();
    });
  }, [canvas, container]);

  // ResizeObserver for responsive behavior (only if enabled)
  useEffect(() => {
    let resizeObserver: ResizeObserver | null = null;

    if (canvas && container && enableAutoResize) {
      resizeObserver = new ResizeObserver(() => autoZoom());
      resizeObserver.observe(container);
    }

    return () => resizeObserver?.disconnect();
  }, [canvas, container, autoZoom, enableAutoResize]);

  return { autoZoom };
};
