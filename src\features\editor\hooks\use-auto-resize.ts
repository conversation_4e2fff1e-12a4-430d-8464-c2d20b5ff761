import { fabric } from "fabric";
import { useCallback } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    if (!canvas || !container) return;

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    // Ensure we have valid dimensions
    if (width <= 0 || height <= 0) {
      console.warn("Container has invalid dimensions for autoZoom", { width, height });
      return;
    }

    // Don't set canvas dimensions here - they should be set by ResizeObserver
    // canvas.setWidth(width);
    // canvas.setHeight(height);

    const center = canvas.getCenter();

    const zoomRatio = 0.85;
    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) return;

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("Workspace has invalid dimensions for autoZoom", { workspaceWidth, workspaceHeight });
      return;
    }

    // @ts-ignore
    const scale = fabric.util.findScaleToFit(localWorkspace, {
      width: width,
      height: height,
    });

    const zoom = zoomRatio * scale;

    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    if (
      canvas.width === undefined ||
      canvas.height === undefined ||
      !viewportTransform
    ) {
      return;
    }

    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];

    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];

    canvas.setViewportTransform(viewportTransform);

    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();
    });
  }, [canvas, container]);

  // ResizeObserver removed - no automatic autoZoom on container resize

  return { autoZoom };
};
